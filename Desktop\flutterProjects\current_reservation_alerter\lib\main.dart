import 'package:flutter/material.dart';
import 'rail_api_service.dart'; // Import your service
import 'captcha_dialog.dart';   // Import your dialog
import 'response_screen.dart';   // Import your response screen

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Indian Rail Demo',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const HomeScreen(),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final RailApiService _apiService = RailApiService();
  bool _isLoading = false;

  void _findTrains() async {
    setState(() => _isLoading = true);

    // 1. Fetch the CAPTCHA image
    final captchaBytes = await _apiService.fetchCaptchaImage();

    if (!mounted) return;

    if (captchaBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Failed to load CAPTCHA. Please try again.")),
      );
      setState(() => _isLoading = false);
      return;
    }

    // 2. Show the dialog and get user input
    final captchaInput = await showCaptchaDialog(context, captchaBytes);

    if (!mounted) return;

    if (captchaInput == null || captchaInput.isEmpty) {
      // User cancelled the dialog
      setState(() => _isLoading = false);
      return;
    }

    // 3. Make the final API call with the captcha text
    final responseText = await _apiService.getTrainsBetweenStations(
      captchaInput: captchaInput,
      journeyDate: "20-07-2025", // Hardcoded for this example
      fromStation: "CHIRALA - CLX",
      toStation: "SECUNDERABAD JN - SC",
    );

    if (!mounted) return;

    setState(() => _isLoading = false);

    if (responseText != null) {
      // 4. Navigate to the new screen to display the response
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ResponseScreen(responseText: responseText),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("An error occurred while fetching trains.")),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Trains'),
      ),
      body: Center(
        child: _isLoading
            ? const CircularProgressIndicator()
            : ElevatedButton(
                onPressed: _findTrains,
                child: const Text('Find Trains Between Stations'),
              ),
      ),
    );
  }
}
