import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:path_provider/path_provider.dart';

class RailApiService {
  late Dio _dio;
  late CookieJar _cookieJar;

  RailApiService() {
    _dio = Dio();
    // Initialize cookie manager to handle sessions automatically
    _initializeCookieManager();
  }

  // Set up the cookie manager to persist cookies across app sessions
  Future<void> _initializeCookieManager() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final cookieJarPath = '${appDocDir.path}/.cookies/';
    _cookieJar = PersistCookieJar(
      ignoreExpires: true,
      storage: FileStorage(cookieJarPath),
    );
    _dio.interceptors.add(CookieManager(_cookieJar));
  }

  // --- Task 1: Fetching the Full Station List ---
  Future<List<String>> fetchStationList() async {
    const url = 'https://www.indianrail.gov.in/enquiry/FetchAutoComplete';
    try {
      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          },
        ),
      );

      if (response.statusCode == 200 && response.data is List) {
        // The response is a List<dynamic>, so we cast it to List<String>
        return List<String>.from(response.data);
      } else {
        throw Exception('Failed to load station list');
      }
    } catch (e) {
      print('Error fetching station list: $e');
      return [];
    }
  }

  // --- Task 2: The Multi-Step Flow to Fetch Trains ---

  // Step 2a: Initialize session and get the CAPTCHA image data
  Future<Uint8List?> fetchCaptchaImage() async {
    try {
      // Step 1 of Python script: Initial request to set cookies.
      // Dio with CookieManager handles this automatically when we make the next call.
      const initialUrl = "https://www.indianrail.gov.in/enquiry/";
      await _dio.get(initialUrl, options: Options(headers: {'User-Agent': 'Mozilla/5.0'}));
      print("Initial page session created.");

      // Step 2 of Python script: Get captcha image
      final ts = DateTime.now().millisecondsSinceEpoch;
      final captchaUrl = "https://www.indianrail.gov.in/enquiry/captchaDraw.png?$ts";
      
      final captchaResponse = await _dio.get(
        captchaUrl,
        options: Options(
          responseType: ResponseType.bytes, // Important: We want the raw image data
          headers: {'User-Agent': 'Mozilla/5.0'}
        ),
      );
      print("Captcha image fetched.");
      return captchaResponse.data;
    } catch (e) {
      print("Error fetching captcha: $e");
      return null;
    }
  }
  
  // Step 2b: Make the final request with user-provided data
  Future<String?> getTrainsBetweenStations({
    required String captchaInput,
    required String journeyDate, // Format: "DD-MM-YYYY"
    required String fromStation, // Format: "STATION NAME - CODE"
    required String toStation,   // Format: "STATION NAME - CODE"
  }) async {
    try {
      final ts = DateTime.now().millisecondsSinceEpoch;
      const url = "https://www.indianrail.gov.in/enquiry/CommonCaptcha";
      
      final params = {
        "inputCaptcha": captchaInput,
        "dt": journeyDate,
        "sourceStation": fromStation,
        "destinationStation": toStation,
        "flexiWithDate": "y",
        "inputPage": "TBIS",
        "language": "en",
        "_": ts
      };

      final response = await _dio.get(
        url,
        queryParameters: params,
        options: Options(headers: {'User-Agent': 'Mozilla/5.0'})
      );

      if(response.statusCode == 200) {
        // The response is JSON. Let's format it nicely for display.
        const jsonEncoder = JsonEncoder.withIndent('  ');
        return jsonEncoder.convert(response.data);
      } else {
        throw Exception('Failed to get train list. Status: ${response.statusCode}');
      }

    } catch (e) {
      print("Error in final train request: $e");
      // Often, errors here are due to an incorrect CAPTCHA.
      // Dio might throw an error if the response is not valid JSON.
      if (e is DioException && e.response?.data != null) {
         return 'Error: ${e.response?.data.toString()}';
      }
      return 'An unknown error occurred.';
    }
  }
}
